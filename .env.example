# TradingAgents-CN 环境变量配置示例
# 🔐 重要：复制此文件为 .env 并填入您的真实API密钥
# ⚠️  警告：.env文件包含敏感信息，请勿提交到Git仓库

# ===== 必需的API密钥 =====

# 🇨🇳 阿里百炼 API 密钥 (推荐，国产大模型，中文优化)
# 获取地址: https://dashscope.aliyun.com/
# 注册阿里云账号 -> 开通百炼服务 -> 获取API密钥
# 格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# 📊 FinnHub API 密钥 (必需，用于获取金融数据)
# 获取地址: https://finnhub.io/
# 免费账户每分钟60次请求，足够日常使用
# 格式: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
FINNHUB_API_KEY=your_finnhub_api_key_here

# ===== 可选的API密钥 =====

# 🌍 OpenAI API 密钥 (可选，需要国外网络)
# 获取地址: https://platform.openai.com/
# 格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_API_KEY=your_openai_api_key_here

# 🔍 Google AI API 密钥 (可选，用于Gemini模型)
# 获取地址: https://ai.google.dev/
GOOGLE_API_KEY=your_google_api_key_here

# 🤖 Anthropic API 密钥 (可选，用于Claude模型)
# 获取地址: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 🚀 DeepSeek V3 API 密钥 (推荐，性价比极高的国产大模型)
# 获取地址: https://platform.deepseek.com/
# 获取步骤：
# 1. 注册DeepSeek账号 -> 登录控制台
# 2. 进入API Keys页面 -> 创建新的API Key
# 3. 复制API Key（格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx）
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_ENABLED=false

# ===== 项目配置 =====

# 结果存储目录
TRADINGAGENTS_RESULTS_DIR=./results

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
TRADINGAGENTS_LOG_LEVEL=INFO

# ===== 数据库配置 =====

# 🔧 数据库启用开关 (默认不启用，系统使用文件缓存)
# 设置为 true 启用对应数据库，false 或不设置则禁用
MONGODB_ENABLED=false
REDIS_ENABLED=false

# 🗄️ MongoDB数据库配置 (用于持久化存储股票数据和分析结果)
# 本地开发: scripts/start_services_alt_ports.bat (端口27018)
# Docker环境: docker-compose up -d (端口27017，主机名mongodb)
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_USERNAME=admin
MONGODB_PASSWORD=tradingagents123
MONGODB_DATABASE=tradingagents
MONGODB_AUTH_SOURCE=admin

# 📦 Redis缓存配置 (用于高速缓存和会话管理)
# 本地开发: scripts/start_services_alt_ports.bat (端口6380)
# Docker环境: docker-compose up -d (端口6379，主机名redis)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=tradingagents123
REDIS_DB=0

# ===== Reddit API 配置 (可选) =====
# 用于获取社交媒体情绪数据
# 获取地址: https://www.reddit.com/prefs/apps

# Reddit 客户端ID
REDDIT_CLIENT_ID=your_reddit_client_id

# Reddit 客户端密钥
REDDIT_CLIENT_SECRET=your_reddit_client_secret

# Reddit 用户代理
REDDIT_USER_AGENT=TradingAgents-CN/1.0

# ===== 使用说明 =====
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 编辑 .env 文件，填入您的真实API密钥
# 3. 至少需要配置 DASHSCOPE_API_KEY 和 FINNHUB_API_KEY
# 4. 运行 python -m cli.main config 检查配置状态
# 5. 运行 python -m cli.main test 验证配置是否正确
#
# ===== Docker 部署说明 =====
# 如果使用 Docker 部署，需要修改以下配置：
# 1. 启用数据库:
#    MONGODB_ENABLED=true
#    REDIS_ENABLED=true
# 2. 修改主机名:
#    MONGODB_HOST=mongodb
#    REDIS_HOST=redis
# 3. 启动服务:
#    docker-compose up -d
# 4. 访问地址:
#    - Web应用: http://localhost:8501
#    - Redis管理: http://localhost:8081
#    - MongoDB管理: http://localhost:8082
