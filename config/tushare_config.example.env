# Tushare配置示例文件
# 复制此文件为 .env 并填入您的实际配置

# ==================== Tushare API配置 ====================

# Tushare API Token (必需)
# 获取方式: 
# 1. 注册Tushare账号: https://tushare.pro/register
# 2. 登录后在个人中心获取Token
TUSHARE_TOKEN=your_tushare_token_here

# ==================== 数据源配置 ====================

# 默认A股数据源 (推荐: tushare, 备用: akshare, baostock, 已弃用: tdx)
DEFAULT_CHINA_DATA_SOURCE=tushare

# 数据缓存配置
ENABLE_DATA_CACHE=true
CACHE_EXPIRE_HOURS=24

# ==================== API调用限制 ====================

# API调用频率限制 (每分钟最大调用次数)
TUSHARE_API_RATE_LIMIT=200

# API超时设置 (秒)
TUSHARE_API_TIMEOUT=30

# ==================== 缓存配置 ====================

# 缓存类型 (file, redis, mongodb)
CACHE_TYPE=file

# Redis配置 (如果使用Redis缓存)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# MongoDB配置 (如果使用MongoDB缓存)
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=tradingagents
MONGODB_USERNAME=
MONGODB_PASSWORD=

# ==================== 日志配置 ====================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 是否启用详细日志
ENABLE_VERBOSE_LOGGING=false

# ==================== 成本跟踪配置 ====================

# 是否启用成本跟踪
ENABLE_COST_TRACKING=true

# 成本警告阈值 (人民币)
COST_ALERT_THRESHOLD=100.0

# ==================== 数据质量配置 ====================

# 数据验证开关
ENABLE_DATA_VALIDATION=true

# 异常数据处理方式 (skip, warn, error)
INVALID_DATA_HANDLING=warn

# ==================== 性能优化配置 ====================

# 并发请求数量
MAX_CONCURRENT_REQUESTS=5

# 批量请求大小
BATCH_REQUEST_SIZE=100

# 连接池大小
CONNECTION_POOL_SIZE=10

# ==================== 备用数据源配置 ====================

# 备用数据源列表 (用逗号分隔)
FALLBACK_DATA_SOURCES=akshare,baostock

# 自动切换到备用数据源
AUTO_FALLBACK_ENABLED=true

# ==================== 特殊功能配置 ====================

# 是否启用实时数据
ENABLE_REALTIME_DATA=false

# 是否启用财务数据
ENABLE_FINANCIAL_DATA=true

# 是否启用技术指标
ENABLE_TECHNICAL_INDICATORS=true

# 是否启用新闻数据
ENABLE_NEWS_DATA=false
